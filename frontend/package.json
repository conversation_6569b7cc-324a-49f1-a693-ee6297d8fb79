{"name": "frontend", "version": "2.1.2", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "npm run build-only", "build:with-types": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "version:patch": "node scripts/bump-version.cjs patch", "version:minor": "node scripts/bump-version.cjs minor", "version:major": "node scripts/bump-version.cjs major", "version:custom": "node scripts/bump-version.cjs custom", "version:current": "node -e \"console.log(require('./package.json').version)\"", "version:check": "node scripts/check-version.cjs", "analyze:performance": "node ../scripts/performance-analysis.js", "test:socket-performance": "node ../scripts/socket-performance-test.js", "analyze:bundle": "npm run build && npx webpack-bundle-analyzer dist/assets/*.js", "test:memory": "node ../scripts/socket-performance-test.js --connections 50 --duration 30", "lighthouse": "npx lighthouse http://localhost:5173 --output=html --output-path=lighthouse-report.html"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.3.0", "@vueuse/head": "^2.0.0", "axios": "^1.9.0", "daisyui": "^4.12.10", "pinia": "^3.0.1", "socket.io-client": "^4.8.1", "vite-plugin-pwa": "^1.0.0", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0", "workbox-window": "^7.3.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autocannon": "^8.0.0", "autoprefixer": "^10.4.21", "clinic": "^13.0.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lighthouse": "^12.6.1", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "postcss": "^8.5.4", "prettier": "3.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8", "webpack-bundle-analyzer": "^4.10.2"}}